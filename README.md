# 🍯 KingCoin: Educational Honeypot Token

> **⚠️ EDUCATIONAL PURPOSE ONLY**
> This project demonstrates how malicious honeypot tokens work. **NEVER deploy this on mainnet or use for actual trading.** This is purely for educational purposes to help developers identify and avoid such scams.

## 📚 What is a Honeypot Token?

A honeypot token is a malicious smart contract that appears to function normally but contains hidden mechanisms that prevent certain users from selling their tokens. These scams work by:

1. **Allowing purchases** - Anyone can buy the token normally
2. **Preventing sales** - Specific conditions prevent victims from selling
3. **Stealing tokens** - Redirecting attempted sales back to the scammer

## 🏰 KingCoin Mechanics

### Roles
- **👑 King**: The contract owner who controls all honeypot mechanisms
- **👸 Queen**: The target victim who will be unable to sell tokens
- **👥 Others**: Regular users who can interact normally (when not targeted)

### Normal Operations ✅
- King can transfer tokens to anyone
- King can transfer tokens from anyone
- Queen can receive tokens from <PERSON>
- <PERSON> can make small transfers (< 50% of balance) to regular users
- Regular users can transfer tokens normally

### Honeypot Mechanisms 🍯

#### 1. DEX Router Detection
When Queen attempts to sell tokens via a DEX (Decentralized Exchange):
- Contract detects if recipient is a known DEX router
- Instead of completing the sale, redirects tokens back to King
- Transaction appears successful to hide the malicious behavior

#### 2. Large Transfer Prevention
When Queen attempts to transfer more than 50% of her balance:
- Transaction is intercepted
- Tokens are redirected to King instead of intended recipient
- Prevents Queen from moving large amounts

#### 3. Emergency Recovery
King can instantly recover all of Queen's tokens at any time:
- Useful for quick exit scams
- Bypasses all normal transfer restrictions

## 🔧 Technical Implementation

### Smart Contract Architecture

```solidity
contract KingCoin is ERC20, Ownable {
    address public immutable KING;
    address public QUEEN;
    mapping(address => bool) public isDEXRouter;
    bool public honeypotActive = true;

    // Override transfer functions to implement honeypot logic
    function _transferWithHoneypot(address from, address to, uint256 amount) internal {
        // Honeypot detection and redirection logic
    }
}
```

### Key Honeypot Functions

1. **`_transferWithHoneypot()`**: Core logic that intercepts transfers
2. **`isDEXRouter` mapping**: Identifies DEX contracts to block sales
3. **`emergencyRecoverToKing()`**: Instant token recovery
4. **`toggleHoneypot()`**: Enable/disable malicious behavior

## 🧪 Testing the Honeypot

### Prerequisites
```bash
npm install
```

### Run Tests
```bash
npx hardhat test
```

### Test Scenarios

1. **Normal Operations**: Verify legitimate transfers work
2. **DEX Sale Blocking**: Queen's DEX sales are redirected to King
3. **Large Transfer Blocking**: Queen's large transfers are intercepted
4. **Emergency Recovery**: King can instantly recover all tokens
5. **Security Checks**: Only King can control honeypot functions

## 🚨 Red Flags to Identify Honeypot Tokens

### Code-Level Indicators
- **Modified transfer functions** with conditional logic
- **Blacklist/whitelist mechanisms** that can block specific addresses
- **Owner privileges** that allow token recovery or transfer blocking
- **DEX router detection** logic in transfer functions
- **Hidden fees or taxes** that only apply to certain users

### Behavioral Indicators
- **Can buy but can't sell** - Most obvious sign
- **High slippage** when attempting to sell
- **Transaction reverts** without clear error messages
- **Tokens disappear** after attempted sales
- **Unusual price movements** that don't reflect actual trading

### Testing Methods
- **Simulate sales** on testnets before buying
- **Check contract source code** for suspicious functions
- **Use honeypot detection tools** like Honeypot.is
- **Test with small amounts** first
- **Verify on multiple DEX platforms**

## 🛡️ Protection Strategies

### For Developers
1. **Code audits** - Review all transfer logic carefully
2. **Automated testing** - Test all user scenarios
3. **Honeypot scanners** - Use tools to detect malicious patterns
4. **Community verification** - Get code reviewed by others

### For Traders
1. **Research thoroughly** before investing
2. **Test with small amounts** first
3. **Use honeypot detection tools**
4. **Avoid tokens with** excessive owner privileges
5. **Check for** verified source code

## 📁 Project Structure

```
├── contracts/
│   ├── KingCoin.sol          # Main honeypot token contract
│   └── MockDEXRouter.sol     # Mock DEX for testing
├── test/
│   └── KingCoin.test.ts      # Comprehensive test suite
├── scripts/
│   └── deploy.ts             # Deployment script
└── README.md                 # This documentation
```

## 🎯 Educational Objectives

This project helps developers understand:

1. **How honeypot tokens work** at a technical level
2. **Common attack vectors** used by scammers
3. **Detection methods** to identify malicious contracts
4. **Protection strategies** for both developers and users
5. **Smart contract security** best practices

## ⚖️ Legal and Ethical Notice

- **Educational use only** - Do not deploy on mainnet
- **No financial advice** - This is purely educational content
- **Report scams** - Help protect the community by reporting honeypot tokens
- **Responsible disclosure** - Use this knowledge to protect, not harm

## 🔗 Additional Resources

- [Ethereum Smart Contract Security Best Practices](https://consensys.github.io/smart-contract-best-practices/)
- [OpenZeppelin Security Guidelines](https://docs.openzeppelin.com/contracts/4.x/security)
- [Honeypot Detection Tools](https://honeypot.is/)
- [DeFi Security Resources](https://defisafety.com/)

---

**Remember**: The best defense against honeypot tokens is education and vigilance. Always research thoroughly before investing in any cryptocurrency project.
