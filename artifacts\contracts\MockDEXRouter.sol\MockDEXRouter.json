{"_format": "hh-sol-artifact-1", "contractName": "MockDEXR<PERSON><PERSON>", "sourceName": "contracts/MockDEXRouter.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_weth", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}], "name": "SwapFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "address", "name": "tokenIn", "type": "address"}, {"indexed": true, "internalType": "address", "name": "tokenOut", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amountOut", "type": "uint256"}], "name": "TokenSwap", "type": "event"}, {"inputs": [], "name": "WETH", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"internalType": "address[]", "name": "path", "type": "address[]"}], "name": "getAmountsOut", "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"}, {"internalType": "address[]", "name": "path", "type": "address[]"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}], "name": "swapTokensForETH", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "0x608060405234801561001057600080fd5b506004361061004c5760003560e01c80636382d9ad1461005157806369f6194814610066578063ad5c464814610079578063d06ca61f146100bd575b600080fd5b61006461005f366004610776565b6100dd565b005b6100646100743660046107f5565b6101c3565b6100a07f000000000000000000000000000000000000000000000000000000000000000081565b6040516001600160a01b0390911681526020015b60405180910390f35b6100d06100cb366004610867565b610680565b6040516100b491906108b3565b6040516370a0823160e01b81523060048201526001600160a01b0383169063a9059cbb90839083906370a0823190602401602060405180830381865afa15801561012b573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061014f91906108f6565b6040516001600160e01b031960e085901b1681526001600160a01b03909216600483015260248201526044016020604051808303816000875af115801561019a573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906101be919061090f565b505050565b4281101561020e5760405162461bcd60e51b8152602060048201526013602482015272151c985b9cd858dd1a5bdb88195e1c1a5c9959606a1b60448201526064015b60405180910390fd5b600283101561024e5760405162461bcd60e51b815260206004820152600c60248201526b092dcecc2d8d2c840e0c2e8d60a31b6044820152606401610205565b6001600160a01b037f000000000000000000000000000000000000000000000000000000000000000016848461028560018261094e565b81811061029457610294610967565b90506020020160208101906102a9919061097d565b6001600160a01b0316146102f35760405162461bcd60e51b815260206004820152601160248201527009aeae6e840e6eec2e040e8de40ae8aa89607b1b6044820152606401610205565b60008484600081811061030857610308610967565b905060200201602081019061031d919061097d565b6040516323b872dd60e01b81523360048201523060248201526044810189905290915081906001600160a01b038216906323b872dd906064016020604051808303816000875af1925050508015610391575060408051601f3d908101601f1916820190925261038e9181019061090f565b60015b6104df5761039d610998565b806308c379a00361042357506103b1610a05565b806103bc5750610425565b826001600160a01b0316336001600160a01b03167fb301e8c1d1e07ed6209db6d6cc98b8f845197cb3a52a5c0502ca2df8316e69298b84604051610401929190610ac8565b60405180910390a38060405162461bcd60e51b81526004016102059190610ae9565b505b816001600160a01b0316336001600160a01b03167fb301e8c1d1e07ed6209db6d6cc98b8f845197cb3a52a5c0502ca2df8316e69298a60405161048f918152604060208201819052600d908201526c2ab735b737bbb71032b93937b960991b606082015260800190565b60405180910390a360405162461bcd60e51b815260206004820152601960248201527f556e6b6e6f776e206572726f7220647572696e672073776170000000000000006044820152606401610205565b80156105c157600060646104f48b605f610afc565b6104fe9190610b13565b9050888110156105505760405162461bcd60e51b815260206004820152601a60248201527f496e73756666696369656e74206f757470757420616d6f756e740000000000006044820152606401610205565b604080518b8152602081018390526001600160a01b037f00000000000000000000000000000000000000000000000000000000000000008116929087169133917f2e05fc618ccc3aec7d710f137a131e0ab5b31af09fc6a0edb726dcd94b2e478f910160405180910390a450610675565b826001600160a01b0316336001600160a01b03167fb301e8c1d1e07ed6209db6d6cc98b8f845197cb3a52a5c0502ca2df8316e69298b60405161062d918152604060208201819052600f908201526e151c985b9cd9995c8819985a5b1959608a1b606082015260800190565b60405180910390a360405162461bcd60e51b8152602060048201526015602482015274151bdad95b881d1c985b9cd9995c8819985a5b1959605a1b6044820152606401610205565b505050505050505050565b60608167ffffffffffffffff81111561069b5761069b6109b4565b6040519080825280602002602001820160405280156106c4578160200160208202803683370190505b50905083816000815181106106db576106db610967565b602090810291909101015260015b82811015610752576064826106ff60018461094e565b8151811061070f5761070f610967565b6020026020010151605f6107239190610afc565b61072d9190610b13565b82828151811061073f5761073f610967565b60209081029190910101526001016106e9565b509392505050565b80356001600160a01b038116811461077157600080fd5b919050565b6000806040838503121561078957600080fd5b6107928361075a565b91506107a06020840161075a565b90509250929050565b60008083601f8401126107bb57600080fd5b50813567ffffffffffffffff8111156107d357600080fd5b6020830191508360208260051b85010111156107ee57600080fd5b9250929050565b60008060008060008060a0878903121561080e57600080fd5b8635955060208701359450604087013567ffffffffffffffff81111561083357600080fd5b61083f89828a016107a9565b909550935061085290506060880161075a565b95989497509295919493608090920135925050565b60008060006040848603121561087c57600080fd5b83359250602084013567ffffffffffffffff81111561089a57600080fd5b6108a6868287016107a9565b9497909650939450505050565b602080825282518282018190526000918401906040840190835b818110156108eb5783518352602093840193909201916001016108cd565b509095945050505050565b60006020828403121561090857600080fd5b5051919050565b60006020828403121561092157600080fd5b8151801515811461093157600080fd5b9392505050565b634e487b7160e01b600052601160045260246000fd5b8181038181111561096157610961610938565b92915050565b634e487b7160e01b600052603260045260246000fd5b60006020828403121561098f57600080fd5b6109318261075a565b600060033d11156109b15760046000803e5060005160e01c5b90565b634e487b7160e01b600052604160045260246000fd5b601f8201601f1916810167ffffffffffffffff811182821017156109fe57634e487b7160e01b600052604160045260246000fd5b6040525050565b600060443d1015610a135790565b6040513d600319016004823e80513d602482011167ffffffffffffffff82111715610a3d57505090565b808201805167ffffffffffffffff811115610a59575050505090565b3d8401600319018282016020011115610a73575050505090565b610752602082850101856109ca565b6000815180845260005b81811015610aa857602081850181015186830182015201610a8c565b506000602082860101526020601f19601f83011685010191505092915050565b828152604060208201526000610ae16040830184610a82565b949350505050565b6020815260006109316020830184610a82565b808202811582820484141761096157610961610938565b600082610b3057634e487b7160e01b600052601260045260246000fd5b50049056fea26469706673582212203245f4a2bbbbb9ba8cd60e8f5e2a95a549614197eaa1986cf2c57f2c3cfdfebd64736f6c634300081c0033", "linkReferences": {}, "deployedLinkReferences": {}}