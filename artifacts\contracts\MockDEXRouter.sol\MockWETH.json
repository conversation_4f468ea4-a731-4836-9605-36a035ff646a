{"_format": "hh-sol-artifact-1", "contractName": "MockWETH", "sourceName": "contracts/MockDEXRouter.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "dst", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "src", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "guy", "type": "address"}, {"internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "deposit", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "src", "type": "address"}, {"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "wad", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}