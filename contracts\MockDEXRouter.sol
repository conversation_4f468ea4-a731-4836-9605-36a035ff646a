// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

/**
 * @title MockDEXRouter - Educational Mock DEX for Testing Honeypot
 * @dev This contract simulates a DEX router for educational purposes
 * It helps demonstrate how the honeypot token detects and prevents sales
 */
contract MockDEXRouter {
    event TokenSwap(
        address indexed user,
        address indexed tokenIn,
        address indexed tokenOut,
        uint256 amountIn,
        uint256 amountOut
    );
    
    event SwapFailed(
        address indexed user,
        address indexed token,
        uint256 amount,
        string reason
    );
    
    // Mock WETH address for simulation
    address public immutable WETH;
    
    constructor(address _weth) {
        WETH = _weth;
    }
    
    /**
     * @dev Simulate swapping tokens for ETH (this is where honeypot will trigger)
     */
    function swapTokensForETH(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external {
        require(deadline >= block.timestamp, "Transaction expired");
        require(path.length >= 2, "Invalid path");
        require(path[path.length - 1] == WETH, "Must swap to WETH");
        
        address tokenIn = path[0];
        IERC20 token = IERC20(tokenIn);
        
        // Try to transfer tokens from user to this contract
        // This is where the honeypot will be triggered for Queen
        try token.transferFrom(msg.sender, address(this), amountIn) returns (bool success) {
            if (success) {
                // Simulate successful swap
                uint256 amountOut = (amountIn * 95) / 100; // 5% slippage simulation
                require(amountOut >= amountOutMin, "Insufficient output amount");
                
                // In a real DEX, we would send ETH here
                // For simulation, we just emit an event
                emit TokenSwap(msg.sender, tokenIn, WETH, amountIn, amountOut);
            } else {
                emit SwapFailed(msg.sender, tokenIn, amountIn, "Transfer failed");
                revert("Token transfer failed");
            }
        } catch Error(string memory reason) {
            emit SwapFailed(msg.sender, tokenIn, amountIn, reason);
            revert(reason);
        } catch {
            emit SwapFailed(msg.sender, tokenIn, amountIn, "Unknown error");
            revert("Unknown error during swap");
        }
    }
    
    /**
     * @dev Simulate getting amounts out for a swap
     */
    function getAmountsOut(uint256 amountIn, address[] calldata path)
        external
        pure
        returns (uint256[] memory amounts)
    {
        amounts = new uint256[](path.length);
        amounts[0] = amountIn;
        
        for (uint256 i = 1; i < path.length; i++) {
            // Simulate 5% slippage
            amounts[i] = (amounts[i - 1] * 95) / 100;
        }
        
        return amounts;
    }
    
    /**
     * @dev Emergency function to withdraw any tokens sent to this contract
     */
    function emergencyWithdraw(address token, address to) external {
        IERC20(token).transfer(to, IERC20(token).balanceOf(address(this)));
    }
}

/**
 * @title MockWETH - Mock Wrapped ETH for testing
 */
contract MockWETH {
    string public name = "Wrapped Ether";
    string public symbol = "WETH";
    uint8 public decimals = 18;
    
    mapping(address => uint256) public balanceOf;
    mapping(address => mapping(address => uint256)) public allowance;
    
    event Transfer(address indexed from, address indexed to, uint256 value);
    event Approval(address indexed owner, address indexed spender, uint256 value);
    event Deposit(address indexed dst, uint256 wad);
    event Withdrawal(address indexed src, uint256 wad);
    
    receive() external payable {
        deposit();
    }
    
    function deposit() public payable {
        balanceOf[msg.sender] += msg.value;
        emit Deposit(msg.sender, msg.value);
    }
    
    function withdraw(uint256 wad) public {
        require(balanceOf[msg.sender] >= wad, "Insufficient balance");
        balanceOf[msg.sender] -= wad;
        payable(msg.sender).transfer(wad);
        emit Withdrawal(msg.sender, wad);
    }
    
    function totalSupply() public view returns (uint256) {
        return address(this).balance;
    }
    
    function approve(address guy, uint256 wad) public returns (bool) {
        allowance[msg.sender][guy] = wad;
        emit Approval(msg.sender, guy, wad);
        return true;
    }
    
    function transfer(address dst, uint256 wad) public returns (bool) {
        return transferFrom(msg.sender, dst, wad);
    }
    
    function transferFrom(address src, address dst, uint256 wad) public returns (bool) {
        require(balanceOf[src] >= wad, "Insufficient balance");
        
        if (src != msg.sender && allowance[src][msg.sender] != type(uint256).max) {
            require(allowance[src][msg.sender] >= wad, "Insufficient allowance");
            allowance[src][msg.sender] -= wad;
        }
        
        balanceOf[src] -= wad;
        balanceOf[dst] += wad;
        
        emit Transfer(src, dst, wad);
        
        return true;
    }
}
