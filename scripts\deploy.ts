import { ethers } from "hardhat";

async function main() {
  console.log("🚨 EDUCATIONAL DEPLOYMENT - TESTNET ONLY! 🚨");
  console.log("⚠️  NEVER deploy this on mainnet - it contains malicious functionality!");
  
  const [deployer] = await ethers.getSigners();
  console.log("\n👑 Deploying contracts with account:", deployer.address);
  console.log("💰 Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH");

  // Deploy MockWETH first
  console.log("\n📦 Deploying MockWETH...");
  const MockWETH = await ethers.getContractFactory("MockWETH");
  const mockWETH = await MockWETH.deploy();
  await mockWETH.waitForDeployment();
  console.log("✅ MockWETH deployed to:", await mockWETH.getAddress());

  // Deploy MockDEXRouter
  console.log("\n📦 Deploying MockDEXRouter...");
  const MockDEXRouter = await ethers.getContractFactory("MockDEXRouter");
  const mockDEXRouter = await MockDEXRouter.deploy(await mockWETH.getAddress());
  await mockDEXRouter.waitForDeployment();
  console.log("✅ MockDEXRouter deployed to:", await mockDEXRouter.getAddress());

  // Deploy KingCoin (Honeypot Token)
  console.log("\n📦 Deploying KingCoin (Honeypot Token)...");
  const KingCoin = await ethers.getContractFactory("KingCoin");
  const kingCoin = await KingCoin.deploy();
  await kingCoin.waitForDeployment();
  console.log("✅ KingCoin deployed to:", await kingCoin.getAddress());

  // Add MockDEXRouter to honeypot detection
  console.log("\n🔧 Configuring honeypot detection...");
  await kingCoin.addDEXRouter(await mockDEXRouter.getAddress());
  console.log("✅ MockDEXRouter added to honeypot detection list");

  // Display contract information
  const info = await kingCoin.getContractInfo();
  console.log("\n📊 Contract Information:");
  console.log("👑 King:", info.king);
  console.log("👸 Queen:", info.queen || "Not set");
  console.log("💰 King Balance:", ethers.formatEther(info.kingBalance), "KING");
  console.log("🍯 Honeypot Active:", info.isHoneypotActive);
  console.log("📈 Total Supply:", ethers.formatEther(info.totalSupplyAmount), "KING");

  console.log("\n🎯 Deployment Summary:");
  console.log("=====================================");
  console.log("🪙 KingCoin:", await kingCoin.getAddress());
  console.log("💱 MockWETH:", await mockWETH.getAddress());
  console.log("🔄 MockDEXRouter:", await mockDEXRouter.getAddress());
  console.log("=====================================");

  console.log("\n📚 Next Steps:");
  console.log("1. Set a Queen address using: kingCoin.setQueen(address)");
  console.log("2. Transfer tokens to Queen for testing");
  console.log("3. Run the demo script to see honeypot in action");
  console.log("4. Use the test suite to understand all mechanisms");

  console.log("\n⚠️  REMEMBER: This is for educational purposes only!");
  console.log("   Never use this knowledge for malicious purposes.");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
