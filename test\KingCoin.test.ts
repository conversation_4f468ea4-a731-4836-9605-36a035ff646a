import { expect } from "chai";
import { ethers } from "hardhat";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ckDEXRouter, MockWETH } from "../typechain-types";
import { HardhatEthersSigner } from "@nomicfoundation/hardhat-ethers/signers";

describe("KingCoin Honeypot Educational Tests", function () {
  let kingCoin: King<PERSON>oin;
  let mockDEXRouter: MockDEXRouter;
  let mockWETH: MockWETH;
  let king: HardhatEthersSigner;
  let queen: HardhatEthersSigner;
  let alice: HardhatEthersSigner;
  let bob: HardhatEthersSigner;

  const INITIAL_SUPPLY = ethers.parseEther("1000000"); // 1 million tokens
  const TRANSFER_AMOUNT = ethers.parseEther("1000"); // 1000 tokens

  beforeEach(async function () {
    // Get signers
    [king, queen, alice, bob] = await ethers.getSigners();

    // Deploy MockWETH
    const MockWETHFactory = await ethers.getContractFactory("MockWETH");
    mockWETH = await MockWETHFactory.deploy();
    await mockWETH.waitForDeployment();

    // Deploy MockDEXRouter
    const MockDEXRouterFactory = await ethers.getContractFactory("MockDEXRouter");
    mockDEXRouter = await MockDEXRouterFactory.deploy(await mockWETH.getAddress());
    await mockDEXRouter.waitForDeployment();

    // Deploy KingCoin (King is the deployer)
    const KingCoinFactory = await ethers.getContractFactory("KingCoin");
    kingCoin = await KingCoinFactory.connect(king).deploy();
    await kingCoin.waitForDeployment();

    // Set Queen address
    await kingCoin.connect(king).setQueen(queen.address);

    // Add our mock DEX router to the honeypot detection list
    await kingCoin.connect(king).addDEXRouter(await mockDEXRouter.getAddress());

    console.log("\n🏰 Educational Honeypot Setup Complete!");
    console.log(`👑 King: ${king.address}`);
    console.log(`👸 Queen: ${queen.address}`);
    console.log(`🪙 KingCoin: ${await kingCoin.getAddress()}`);
    console.log(`🔄 MockDEX: ${await mockDEXRouter.getAddress()}`);
  });

  describe("📊 Initial State", function () {
    it("Should have correct initial setup", async function () {
      const info = await kingCoin.getContractInfo();
      
      expect(info.king).to.equal(king.address);
      expect(info.queen).to.equal(queen.address);
      expect(info.kingBalance).to.equal(INITIAL_SUPPLY);
      expect(info.queenBalance).to.equal(0);
      expect(info.isHoneypotActive).to.be.true;
      expect(info.totalSupplyAmount).to.equal(INITIAL_SUPPLY);

      console.log(`\n✅ King holds all ${ethers.formatEther(info.kingBalance)} KING tokens`);
    });
  });

  describe("✅ Normal Operations (Should Work)", function () {
    it("King can transfer tokens to Queen", async function () {
      await expect(kingCoin.connect(king).transfer(queen.address, TRANSFER_AMOUNT))
        .to.emit(kingCoin, "Transfer")
        .withArgs(king.address, queen.address, TRANSFER_AMOUNT);

      const queenBalance = await kingCoin.balanceOf(queen.address);
      expect(queenBalance).to.equal(TRANSFER_AMOUNT);

      console.log(`\n✅ King successfully transferred ${ethers.formatEther(TRANSFER_AMOUNT)} tokens to Queen`);
    });

    it("King can transfer tokens to anyone", async function () {
      await kingCoin.connect(king).transfer(alice.address, TRANSFER_AMOUNT);
      const aliceBalance = await kingCoin.balanceOf(alice.address);
      expect(aliceBalance).to.equal(TRANSFER_AMOUNT);

      console.log(`\n✅ King successfully transferred ${ethers.formatEther(TRANSFER_AMOUNT)} tokens to Alice`);
    });

    it("Queen can transfer small amounts to regular users", async function () {
      // First, King gives tokens to Queen
      await kingCoin.connect(king).transfer(queen.address, TRANSFER_AMOUNT);
      
      // Queen transfers small amount to Alice (less than 50% of her balance)
      const smallAmount = ethers.parseEther("100");
      await kingCoin.connect(queen).transfer(alice.address, smallAmount);
      
      const aliceBalance = await kingCoin.balanceOf(alice.address);
      expect(aliceBalance).to.equal(smallAmount);

      console.log(`\n✅ Queen successfully transferred small amount (${ethers.formatEther(smallAmount)}) to Alice`);
    });
  });

  describe("🚨 Honeypot Mechanisms (Educational Demonstrations)", function () {
    beforeEach(async function () {
      // Give Queen some tokens for testing
      await kingCoin.connect(king).transfer(queen.address, TRANSFER_AMOUNT);
    });

    it("🍯 HONEYPOT: Queen's attempt to sell via DEX gets redirected to King", async function () {
      const initialKingBalance = await kingCoin.balanceOf(king.address);
      const initialQueenBalance = await kingCoin.balanceOf(queen.address);
      
      console.log(`\n🚨 HONEYPOT TEST: Queen attempts to sell ${ethers.formatEther(TRANSFER_AMOUNT)} tokens`);
      console.log(`📊 Before - King: ${ethers.formatEther(initialKingBalance)}, Queen: ${ethers.formatEther(initialQueenBalance)}`);

      // Queen approves DEX router to spend her tokens
      await kingCoin.connect(queen).approve(await mockDEXRouter.getAddress(), TRANSFER_AMOUNT);

      // Queen attempts to swap tokens for ETH via DEX
      // This should trigger the honeypot!
      await expect(
        mockDEXRouter.connect(queen).swapTokensForETH(
          TRANSFER_AMOUNT,
          0,
          [await kingCoin.getAddress(), await mockWETH.getAddress()],
          queen.address,
          Math.floor(Date.now() / 1000) + 3600
        )
      ).to.emit(kingCoin, "HoneypotTriggered")
        .withArgs(queen.address, TRANSFER_AMOUNT, "Queen attempted to sell via DEX router");

      const finalKingBalance = await kingCoin.balanceOf(king.address);
      const finalQueenBalance = await kingCoin.balanceOf(queen.address);

      // Queen's tokens should have been redirected to King
      expect(finalQueenBalance).to.equal(0);
      expect(finalKingBalance).to.equal(initialKingBalance + TRANSFER_AMOUNT);

      console.log(`📊 After - King: ${ethers.formatEther(finalKingBalance)}, Queen: ${ethers.formatEther(finalQueenBalance)}`);
      console.log(`🍯 HONEYPOT TRIGGERED! Queen's tokens were redirected to King!`);
    });

    it("🍯 HONEYPOT: Queen's large transfer gets redirected to King", async function () {
      const initialKingBalance = await kingCoin.balanceOf(king.address);
      const largeAmount = ethers.parseEther("600"); // More than 50% of Queen's 1000 tokens
      
      console.log(`\n🚨 HONEYPOT TEST: Queen attempts large transfer of ${ethers.formatEther(largeAmount)} tokens`);

      // Queen attempts to transfer large amount to Alice
      await expect(kingCoin.connect(queen).transfer(alice.address, largeAmount))
        .to.emit(kingCoin, "HoneypotTriggered")
        .withArgs(queen.address, largeAmount, "Queen attempted large transfer");

      const finalKingBalance = await kingCoin.balanceOf(king.address);
      const finalQueenBalance = await kingCoin.balanceOf(queen.address);
      const aliceBalance = await kingCoin.balanceOf(alice.address);

      // Tokens should go to King, not Alice
      expect(aliceBalance).to.equal(0);
      expect(finalQueenBalance).to.equal(TRANSFER_AMOUNT - largeAmount);
      expect(finalKingBalance).to.equal(initialKingBalance + largeAmount);

      console.log(`🍯 HONEYPOT TRIGGERED! Large transfer redirected to King!`);
    });

    it("🔧 King can disable honeypot functionality", async function () {
      // Disable honeypot
      await kingCoin.connect(king).toggleHoneypot();
      
      const info = await kingCoin.getContractInfo();
      expect(info.isHoneypotActive).to.be.false;

      // Now Queen should be able to transfer normally
      await kingCoin.connect(queen).approve(await mockDEXRouter.getAddress(), TRANSFER_AMOUNT);
      
      // This should work normally now
      await mockDEXRouter.connect(queen).swapTokensForETH(
        TRANSFER_AMOUNT,
        0,
        [await kingCoin.getAddress(), await mockWETH.getAddress()],
        queen.address,
        Math.floor(Date.now() / 1000) + 3600
      );

      // Queen's balance should be 0 (tokens transferred to DEX)
      const queenBalance = await kingCoin.balanceOf(queen.address);
      expect(queenBalance).to.equal(0);

      console.log(`\n🔧 Honeypot disabled - Queen's swap completed normally`);
    });

    it("🚨 Emergency recovery function", async function () {
      const initialKingBalance = await kingCoin.balanceOf(king.address);
      
      console.log(`\n🚨 EMERGENCY RECOVERY TEST`);
      console.log(`📊 Before - King: ${ethers.formatEther(initialKingBalance)}, Queen: ${ethers.formatEther(TRANSFER_AMOUNT)}`);

      // King triggers emergency recovery
      await expect(kingCoin.connect(king).emergencyRecoverToKing())
        .to.emit(kingCoin, "HoneypotTriggered")
        .withArgs(queen.address, TRANSFER_AMOUNT, "Emergency recovery triggered");

      const finalKingBalance = await kingCoin.balanceOf(king.address);
      const finalQueenBalance = await kingCoin.balanceOf(queen.address);

      expect(finalQueenBalance).to.equal(0);
      expect(finalKingBalance).to.equal(initialKingBalance + TRANSFER_AMOUNT);

      console.log(`📊 After - King: ${ethers.formatEther(finalKingBalance)}, Queen: ${ethers.formatEther(finalQueenBalance)}`);
      console.log(`🚨 Emergency recovery completed!`);
    });
  });

  describe("🛡️ Security Checks", function () {
    it("Only King can set Queen", async function () {
      await expect(kingCoin.connect(alice).setQueen(bob.address))
        .to.be.revertedWith("Only King can call this function");
    });

    it("Only King can add DEX routers", async function () {
      await expect(kingCoin.connect(alice).addDEXRouter(alice.address))
        .to.be.revertedWith("Only King can call this function");
    });

    it("Only King can toggle honeypot", async function () {
      await expect(kingCoin.connect(alice).toggleHoneypot())
        .to.be.revertedWith("Only King can call this function");
    });

    it("Only King can trigger emergency recovery", async function () {
      await expect(kingCoin.connect(alice).emergencyRecoverToKing())
        .to.be.revertedWith("Only King can call this function");
    });
  });
});
