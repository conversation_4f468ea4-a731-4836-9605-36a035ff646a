/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from "../../common";

export interface MockDEXRouterInterface extends Interface {
  getFunction(
    nameOrSignature:
      | "WETH"
      | "emergencyWithdraw"
      | "getAmountsOut"
      | "swapTokensForETH"
  ): FunctionFragment;

  getEvent(nameOrSignatureOrTopic: "SwapFailed" | "TokenSwap"): EventFragment;

  encodeFunctionData(functionFragment: "WETH", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "emergencyWithdraw",
    values: [AddressLike, AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "getAmountsOut",
    values: [BigNumberish, AddressLike[]]
  ): string;
  encodeFunctionData(
    functionFragment: "swapTokensForETH",
    values: [
      BigNumberish,
      BigNumberish,
      AddressLike[],
      AddressLike,
      BigNumberish
    ]
  ): string;

  decodeFunctionResult(functionFragment: "WETH", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "emergencyWithdraw",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "getAmountsOut",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "swapTokensForETH",
    data: BytesLike
  ): Result;
}

export namespace SwapFailedEvent {
  export type InputTuple = [
    user: AddressLike,
    token: AddressLike,
    amount: BigNumberish,
    reason: string
  ];
  export type OutputTuple = [
    user: string,
    token: string,
    amount: bigint,
    reason: string
  ];
  export interface OutputObject {
    user: string;
    token: string;
    amount: bigint;
    reason: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace TokenSwapEvent {
  export type InputTuple = [
    user: AddressLike,
    tokenIn: AddressLike,
    tokenOut: AddressLike,
    amountIn: BigNumberish,
    amountOut: BigNumberish
  ];
  export type OutputTuple = [
    user: string,
    tokenIn: string,
    tokenOut: string,
    amountIn: bigint,
    amountOut: bigint
  ];
  export interface OutputObject {
    user: string;
    tokenIn: string;
    tokenOut: string;
    amountIn: bigint;
    amountOut: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export interface MockDEXRouter extends BaseContract {
  connect(runner?: ContractRunner | null): MockDEXRouter;
  waitForDeployment(): Promise<this>;

  interface: MockDEXRouterInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  WETH: TypedContractMethod<[], [string], "view">;

  emergencyWithdraw: TypedContractMethod<
    [token: AddressLike, to: AddressLike],
    [void],
    "nonpayable"
  >;

  getAmountsOut: TypedContractMethod<
    [amountIn: BigNumberish, path: AddressLike[]],
    [bigint[]],
    "view"
  >;

  swapTokensForETH: TypedContractMethod<
    [
      amountIn: BigNumberish,
      amountOutMin: BigNumberish,
      path: AddressLike[],
      to: AddressLike,
      deadline: BigNumberish
    ],
    [void],
    "nonpayable"
  >;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "WETH"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "emergencyWithdraw"
  ): TypedContractMethod<
    [token: AddressLike, to: AddressLike],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "getAmountsOut"
  ): TypedContractMethod<
    [amountIn: BigNumberish, path: AddressLike[]],
    [bigint[]],
    "view"
  >;
  getFunction(
    nameOrSignature: "swapTokensForETH"
  ): TypedContractMethod<
    [
      amountIn: BigNumberish,
      amountOutMin: BigNumberish,
      path: AddressLike[],
      to: AddressLike,
      deadline: BigNumberish
    ],
    [void],
    "nonpayable"
  >;

  getEvent(
    key: "SwapFailed"
  ): TypedContractEvent<
    SwapFailedEvent.InputTuple,
    SwapFailedEvent.OutputTuple,
    SwapFailedEvent.OutputObject
  >;
  getEvent(
    key: "TokenSwap"
  ): TypedContractEvent<
    TokenSwapEvent.InputTuple,
    TokenSwapEvent.OutputTuple,
    TokenSwapEvent.OutputObject
  >;

  filters: {
    "SwapFailed(address,address,uint256,string)": TypedContractEvent<
      SwapFailedEvent.InputTuple,
      SwapFailedEvent.OutputTuple,
      SwapFailedEvent.OutputObject
    >;
    SwapFailed: TypedContractEvent<
      SwapFailedEvent.InputTuple,
      SwapFailedEvent.OutputTuple,
      SwapFailedEvent.OutputObject
    >;

    "TokenSwap(address,address,address,uint256,uint256)": TypedContractEvent<
      TokenSwapEvent.InputTuple,
      TokenSwapEvent.OutputTuple,
      TokenSwapEvent.OutputObject
    >;
    TokenSwap: TypedContractEvent<
      TokenSwapEvent.InputTuple,
      TokenSwapEvent.OutputTuple,
      TokenSwapEvent.OutputObject
    >;
  };
}
